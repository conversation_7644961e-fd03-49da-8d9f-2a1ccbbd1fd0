import React from "react";
import { useOffline } from "@/hooks/useOffline";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";

interface OfflineIndicatorProps {
  className?: string;
  showWhenOnline?: boolean;
  position?: "top-left" | "top-right" | "bottom-left" | "bottom-right";
}

export const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({
  className = "",
  showWhenOnline = false,
  position = "top-right",
}) => {
  const { state } = useOffline();
  const { networkStatus, queueStats, showOfflineIndicator } = state;
  const { state: themeState } = useTheme();
  const mode = themeState?.theme;

  // Don't show if online and showWhenOnline is false
  if (networkStatus.isOnline && !showWhenOnline && !showOfflineIndicator) {
    return null;
  }

  const positionClasses = {
    "top-left": "top-4 left-4",
    "top-right": "top-4 right-4",
    "bottom-left": "bottom-4 left-4",
    "bottom-right": "bottom-4 right-4",
  };

  const getStatusColor = () => {
    if (!networkStatus.isOnline)
      return { backgroundColor: "#EF4444", color: "#FFFFFF" }; // red-500
    if (networkStatus.isSlowConnection)
      return { backgroundColor: "#F59E0B", color: "#FFFFFF" }; // yellow-500
    if (queueStats.total > 0)
      return {
        backgroundColor: THEME_COLORS[mode].PRIMARY,
        color: THEME_COLORS[mode].TEXT_ON_PRIMARY,
      };
    return { backgroundColor: "#10B981", color: "#FFFFFF" }; // green-500
  };

  const getStatusText = () => {
    if (!networkStatus.isOnline) return "Offline";
    if (networkStatus.isSlowConnection) return "Slow Connection";
    if (queueStats.total > 0) return `${queueStats.total} Pending`;
    return "Online";
  };

  const getStatusIcon = () => {
    if (!networkStatus.isOnline) return "📴";
    if (networkStatus.isSlowConnection) return "🐌";
    if (queueStats.total > 0) return "⏳";
    return "🟢";
  };

  return (
    <div className={`fixed z-50 ${positionClasses[position]} ${className}`}>
      <div
        className="flex items-center space-x-2 px-3 py-2 rounded-lg shadow-lg text-sm font-medium transition-all duration-300 ease-in-out"
        style={getStatusColor()}
      >
        <span className="text-base">{getStatusIcon()}</span>
        <span>{getStatusText()}</span>

        {queueStats.total > 0 && (
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            <span className="text-xs">
              ({queueStats.byPriority.high}H, {queueStats.byPriority.medium}M,{" "}
              {queueStats.byPriority.low}L)
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default OfflineIndicator;
