// list out all folders in the directory
const fs = require("fs");
const path = require("path");

// define the path to the directory
const folderPath = path.join(__dirname);

// list out all folders in the directory
const items = fs.readdirSync(folderPath);

// filter only folders and print them
const folders = items.filter((item) => {
  const itemPath = path.join(folderPath, item);
  return fs.statSync(itemPath).isDirectory();
});

console.log("Folders in current directory:");
folders.forEach((folder) => {
  console.log(folder);
});

AdminHeader
AdminWrapper
BackButton
BasicTextarea
Calendar
CircularImagePreview
CodeEditor
Collapser
CollapsibleMenu
Container
CreateNewRoomModal
CustomSelect
DateRange
Deployment
DisplayDomainUrl
DropdownOptions
Editor
ErrorBoundary
ExportButton
HeaderLogo
HorizontalNavbar
ImagePreviewModal
InteractiveButton
InteractiveMap
LandingPage
LazyLoad
Loader
LoadingIndicator
MkdButton
MkdCalendar
MkdControlledInput
MkdDebounceInput
MkdFileTable
MKDForm
MkdListTable
MkdLoader
MkdPasswordInput
MkdPopover
MkdSimpleTable
MkdTabContainer
MkdWizardContainer
Modal
ModalSidebar
MultipleAnswer
MultiSelect
Notifications
OfflineAwareForm
OfflineExample
OfflineIndicator
OfflineNotifications
OfflineStatusBar
PaginationBar
ProfileImageUpload
ProgressBar
PublicHeader
PublicWrapper
QrCodeGenerator
QrCodeReader
RatingStar
RouteChangeModal
SearchableDropdown
SessionExpiredModal
SyncDashboard
SyncStatusIndicator
ThemeToggle
Title
TitleDetail
Toast
TopBarSticky
TopHeader
UploadConfig
UserProfile
ViewModelItem
